#!/usr/bin/env python3
"""
Test script to verify enhanced classification logic for standalone vs consolidated statements
"""

import sys
import os
from pathlib import Path

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_classification_prompt():
    """Test the enhanced classification prompt logic"""
    
    print("🧪 Testing Enhanced Classification Logic")
    print("=" * 60)
    
    # Import the classifier
    try:
        from classify import EnhancedPDFFinancialClassifier
        classifier = EnhancedPDFFinancialClassifier()
        
        print("✅ Classifier initialized successfully")
        print(f"📋 Standard categories: {len(classifier.FINANCIAL_CATEGORIES)}")
        
        # Test the prompt content
        prompt = classifier.ENHANCED_CLASSIFICATION_PROMPT
        
        print("\n🔍 Checking Enhanced Prompt Features:")
        print("-" * 50)
        
        # Check for consolidated detection logic
        consolidated_checks = [
            ("STEP 1: First check if the page contains ONLY consolidated", "3-step consolidated detection"),
            ("ONLY consolidated statements exist", "Consolidated rejection logic"),
            ("BOTH standalone AND consolidated", "Mixed page handling"),
            ("WITHOUT \"Consolidated\" prefix", "Standalone requirement"),
            ("REJECT if ONLY consolidated version exists", "Explicit rejection rule")
        ]
        
        for check_text, description in consolidated_checks:
            if check_text in prompt:
                print(f"  ✅ {description}: Found")
            else:
                print(f"  ❌ {description}: Missing")
        
        print("\n📊 Testing Classification Rules:")
        print("-" * 50)
        
        # Test specific rule improvements
        rule_checks = [
            ("Balance Sheet\" OR \"Statement of Financial Position\" OR \"Statement of Financial Condition\" (WITHOUT \"Consolidated\" prefix)", "Balance Sheet standalone requirement"),
            ("Statement of Profit and Loss\" OR \"Statement of Comprehensive Income\" OR \"Income Statement\" OR \"Profit & Loss Statement\" OR \"Statement of Operations\" (WITHOUT \"Consolidated\" prefix)", "P&L standalone requirement"),
            ("Cash Flow\", \"Cash Flows\", \"Statement of Cash Flow\", \"Statement of Cash Flows\" (WITHOUT \"Consolidated\" prefix)", "Cash Flow standalone requirement"),
            ("Statement of Changes in Equity\" OR \"Statement of Shareholders' Equity\" (WITHOUT \"Consolidated\" prefix)", "Changes in Equity standalone requirement")
        ]
        
        for rule_text, description in rule_checks:
            if rule_text in prompt:
                print(f"  ✅ {description}: Updated")
            else:
                print(f"  ❌ {description}: Not updated")
        
        print("\n🎯 Testing Example Scenarios:")
        print("-" * 50)
        
        # Check for clear examples
        example_checks = [
            ("ONLY Consolidated (REJECT)", "Rejection example"),
            ("BOTH Standalone and Consolidated (ACCEPT)", "Mixed page example"),
            ("ONLY Standalone (ACCEPT)", "Standalone example"),
            ("Irrelevant / Miscellaneous Page", "Proper rejection classification")
        ]
        
        for example_text, description in example_checks:
            if example_text in prompt:
                print(f"  ✅ {description}: Provided")
            else:
                print(f"  ❌ {description}: Missing")
        
        print("\n📝 Prompt Analysis Summary:")
        print("-" * 50)
        
        # Count key terms
        consolidated_mentions = prompt.count("Consolidated")
        standalone_mentions = prompt.count("standalone")
        reject_mentions = prompt.count("REJECT")
        
        print(f"  • 'Consolidated' mentions: {consolidated_mentions}")
        print(f"  • 'standalone' mentions: {standalone_mentions}")
        print(f"  • 'REJECT' mentions: {reject_mentions}")
        
        # Check for contradictions
        print(f"\n🔍 Checking for Contradictions:")
        print("-" * 50)
        
        # Look for old contradictory patterns
        contradictions = [
            ("Consolidated Balance Sheet\" is accepted", "Old acceptance rule"),
            ("Consolidated Statement of Cash Flows\" is accepted", "Old cash flow rule"),
            ("Be MORE FLEXIBLE with consolidated presentations", "Old flexibility rule")
        ]
        
        contradiction_found = False
        for contradiction_text, description in contradictions:
            if contradiction_text in prompt:
                print(f"  ❌ {description}: Still present (contradiction!)")
                contradiction_found = True
            else:
                print(f"  ✅ {description}: Removed")
        
        if not contradiction_found:
            print(f"\n✅ No contradictions found - Logic is consistent!")
        else:
            print(f"\n❌ Contradictions detected - Needs further fixing!")
        
        print(f"\n🎯 Enhanced Classification Test Results:")
        print("=" * 60)
        
        if not contradiction_found and consolidated_mentions >= 10 and reject_mentions >= 4:
            print("✅ Enhanced classification logic appears to be correctly implemented!")
            print("📋 The classifier should now properly:")
            print("   • Reject pages with ONLY consolidated statements")
            print("   • Accept pages with standalone statements (even if consolidated also present)")
            print("   • Provide clear examples for the AI model")
            print("   • Have consistent, non-contradictory rules")
            return True
        else:
            print("❌ Enhanced classification logic needs further refinement!")
            return False
            
    except Exception as e:
        print(f"❌ Error testing classification: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_validation_method():
    """Test the validation method"""
    print(f"\n🧪 Testing Validation Method:")
    print("-" * 40)
    
    try:
        from classify import EnhancedPDFFinancialClassifier
        classifier = EnhancedPDFFinancialClassifier()
        
        # Test validation with standard names
        test_cases = [
            ("Statement of Balance Sheet", "Statement of Balance Sheet", "Standard name"),
            ("Statement of Profit and Loss", "Statement of Profit and Loss", "Standard name"),
            ("Statement of Cash Flows", "Statement of Cash Flows", "Standard name"),
            ("Notes to Financial Statements", "Notes to Financial Statements", "Standard name"),
            ("Invalid Classification", "Irrelevant / Miscellaneous Page", "Invalid name fallback"),
            ("Consolidated Balance Sheet", "Irrelevant / Miscellaneous Page", "Non-standard name fallback")
        ]
        
        all_passed = True
        for input_val, expected, description in test_cases:
            result = classifier.validate_classification(input_val)
            if result == expected:
                print(f"  ✅ {description}: {input_val} → {result}")
            else:
                print(f"  ❌ {description}: {input_val} → {result} (expected: {expected})")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error testing validation: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Enhanced Classification Tests")
    print("=" * 60)
    
    # Test 1: Classification prompt logic
    prompt_test_passed = test_classification_prompt()
    
    # Test 2: Validation method
    validation_test_passed = test_validation_method()
    
    print(f"\n🎯 Final Test Results:")
    print("=" * 60)
    
    if prompt_test_passed and validation_test_passed:
        print("✅ All tests PASSED!")
        print("📋 Enhanced classification is ready for use")
        print("🎯 The system should now correctly handle standalone vs consolidated detection")
        sys.exit(0)
    else:
        print("❌ Some tests FAILED!")
        print("🔧 Further refinement needed")
        sys.exit(1)
