#!/usr/bin/env python3
"""
Test script to verify decimal value preservation in debt/equity extraction
"""

import json
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from single_file_debt_equity_processor import SingleFileDebtEquityProcessor

def test_decimal_preservation():
    """Test that decimal values are preserved correctly"""
    
    print("🧪 Testing Decimal Value Preservation")
    print("=" * 50)
    
    # Create processor instance
    processor = SingleFileDebtEquityProcessor()
    
    # Test the safe_numeric_value method with various decimal inputs
    test_cases = [
        # (input_value, expected_output, description)
        (4009.25, 4009.25, "Float input"),
        ("4009.25", 4009.25, "String with decimal"),
        ("₹4,009.25", 4009.25, "Currency symbol with comma and decimal"),
        ("4009.75", 4009.75, "String decimal"),
        (1500.50, 1500.50, "Another float"),
        ("2,778.54", 2778.54, "String with comma and decimal"),
        ("836.10", 836.10, "String decimal"),
        ("243.10", 243.10, "String decimal"),
        (200, 200.0, "Integer input"),
        ("200", 200.0, "String integer"),
        (0, None, "Zero value"),
        ("0.00", None, "Zero decimal string"),
        (None, None, "None input"),
        ("", None, "Empty string"),
    ]
    
    print("🔍 Testing safe_numeric_value method:")
    print("-" * 40)
    
    all_passed = True
    for i, (input_val, expected, description) in enumerate(test_cases, 1):
        try:
            result = processor.safe_numeric_value(input_val)
            
            if result == expected:
                status = "✅ PASS"
            else:
                status = "❌ FAIL"
                all_passed = False
            
            print(f"{i:2d}. {description:25} | Input: {str(input_val):12} | Expected: {str(expected):8} | Got: {str(result):8} | {status}")
            
        except Exception as e:
            print(f"{i:2d}. {description:25} | Input: {str(input_val):12} | ERROR: {e}")
            all_passed = False
    
    print("\n" + "=" * 50)
    
    # Test with mock extraction data containing decimal values
    print("\n🧪 Testing Full Extraction with Decimal Values")
    print("-" * 50)
    
    mock_extraction_data = {
        "company_info": {
            "name": "Test Company Ltd",
            "date": "31/03/2024",
            "year": "2024"
        },
        "currency_unit": "Crores",
        "equity": {
            "share_capital": {
                "name": "Equity Share Capital",
                "value": 200.75,  # Decimal value
                "shares": 20000000
            },
            "other_equity": [
                {"name": "Securities Premium", "value": 1126.44},  # Decimal value
                {"name": "Retained Earnings", "value": 543.21}    # Decimal value
            ],
            "total_equity": 1870.40  # Decimal value
        },
        "long_term_debt": {
            "total_value": 2778.54,  # Decimal value
            "interest_rates": "8.5-9.5%",
            "secured_debt": {
                "total": 2778.54,
                "details": [
                    {"name": "Term Loan - Bank A", "amount": 1500.25, "lender": "Bank A", "security": "secured"},  # Decimal
                    {"name": "Term Loan - Bank B", "amount": 1278.29, "lender": "Bank B", "security": "secured"}   # Decimal
                ]
            },
            "unsecured_debt": {
                "total": 0,
                "details": []
            }
        },
        "short_term_debt": {
            "total_value": 1079.85,  # Decimal value
            "interest_rates": "6.5-8.5%",
            "current_borrowings": 836.75,  # Decimal value
            "current_maturities": 243.10,  # Decimal value
            "secured_debt": {
                "total": 836.75,
                "details": [
                    {"name": "Working Capital Loan - Bank A", "amount": 400.25, "lender": "Bank A", "security": "secured"},  # Decimal
                    {"name": "Cash Credit Facility - Bank B", "amount": 300.50, "lender": "Bank B", "security": "secured"}, # Decimal
                    {"name": "Bank Overdraft", "amount": 136.00, "lender": "Multiple Banks", "security": "secured"}         # Decimal
                ]
            },
            "unsecured_debt": {
                "total": 243.10,
                "details": [
                    {"name": "Current maturities of long-term debt", "amount": 200.60, "lender": "Banks/FIs", "security": "unsecured"},  # Decimal
                    {"name": "Unsecured loan from related party", "amount": 42.50, "lender": "Related Party", "security": "unsecured"}   # Decimal
                ]
            }
        },
        "other_liabilities": {
            "trade_payables": 4150.30,  # Decimal value
            "provisions": 917.70,       # Decimal value
            "deferred_tax_liabilities": 1500.25,  # Decimal value
            "other_current_liabilities": 850.45   # Decimal value
        }
    }
    
    try:
        result = processor.build_final_output_clean(mock_extraction_data, "INR", 2024)
        
        print("✅ Full extraction completed successfully!")
        print("\n📊 Checking Decimal Preservation in Results:")
        print("-" * 50)
        
        # Check debt_equity_analysis values
        debt_analysis = result.get('debt_equity_analysis', [])
        if debt_analysis and debt_analysis[0]:
            analysis = debt_analysis[0]
            equity_val = analysis.get('equity', {}).get('value')
            long_term_val = analysis.get('long_term', {}).get('value')
            short_term_val = analysis.get('short_term', {}).get('value')
            
            print(f"📈 Debt-Equity Analysis Values:")
            print(f"  • Equity: {equity_val} (Expected: 1870.4)")
            print(f"  • Long-term: {long_term_val} (Expected: 2778.54)")
            print(f"  • Short-term: {short_term_val} (Expected: 1079.85)")
            
            # Verify decimal preservation
            decimal_checks = [
                (equity_val, 1870.4, "Equity"),
                (long_term_val, 2778.54, "Long-term"),
                (short_term_val, 1079.85, "Short-term")
            ]
            
            for actual, expected, name in decimal_checks:
                if actual == expected:
                    print(f"  ✅ {name} decimal preserved correctly")
                else:
                    print(f"  ❌ {name} decimal NOT preserved: got {actual}, expected {expected}")
                    all_passed = False
        
        # Check detailed short-term debt values
        equity_liability = result.get('equity_liability', {})
        short_term_debt = equity_liability.get('short_term_debt', {})
        
        print(f"\n🔍 Short-term Debt Detail Values:")
        senior_details = short_term_debt.get('senior_debt', {}).get('investor_details', [])
        for detail in senior_details:
            if detail.get('value'):
                print(f"  • {detail.get('name', 'N/A')}: {detail.get('value')} (decimal preserved: {'✅' if isinstance(detail.get('value'), float) else '❌'})")
        
        junior_details = short_term_debt.get('junior_debt', {}).get('investor_details', [])
        for detail in junior_details:
            if detail.get('value'):
                print(f"  • {detail.get('name', 'N/A')}: {detail.get('value')} (decimal preserved: {'✅' if isinstance(detail.get('value'), float) else '❌'})")
        
        if all_passed:
            print(f"\n✅ All decimal preservation tests PASSED!")
            print(f"📝 Decimal values are now correctly preserved in extraction.")
        else:
            print(f"\n❌ Some decimal preservation tests FAILED!")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Full extraction test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_decimal_preservation()
    sys.exit(0 if success else 1)
