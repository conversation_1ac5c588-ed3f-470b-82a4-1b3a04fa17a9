#!/usr/bin/env python3
"""
Test script to verify enhanced short-term debt extraction
"""

import json
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from single_file_debt_equity_processor import SingleFileDebtEquityProcessor

def test_enhanced_extraction():
    """Test the enhanced short-term debt extraction logic"""
    
    print("🧪 Testing Enhanced Short-term Debt Extraction")
    print("=" * 60)
    
    # Create processor instance
    processor = SingleFileDebtEquityProcessor()
    
    # Mock extraction data with detailed short-term debt breakdown
    mock_extraction_data = {
        "company_info": {
            "name": "Test Company Ltd",
            "date": "31/03/2024",
            "year": "2024"
        },
        "currency_unit": "Crores",
        "equity": {
            "share_capital": {
                "name": "Equity Share Capital",
                "value": 200.00,
                "shares": ********
            },
            "other_equity": [
                {"name": "Securities Premium", "value": 1126.44},
                {"name": "Retained Earnings", "value": 543.21}
            ],
            "total_equity": 1869.65
        },
        "long_term_debt": {
            "total_value": 2778.54,
            "interest_rates": "8.5-9.5%",
            "secured_debt": {
                "total": 2778.54,
                "details": [
                    {"name": "Term Loan - Bank A", "amount": 1500.00, "lender": "Bank A", "security": "secured"},
                    {"name": "Term Loan - Bank B", "amount": 1278.54, "lender": "Bank B", "security": "secured"}
                ]
            },
            "unsecured_debt": {
                "total": 0,
                "details": []
            }
        },
        "short_term_debt": {
            "total_value": 1079.20,
            "interest_rates": "6.5-8.5%",
            "current_borrowings": 836.10,
            "current_maturities": 243.10,
            "secured_debt": {
                "total": 836.10,
                "details": [
                    {"name": "Working Capital Loan - Bank A", "amount": 400.00, "lender": "Bank A", "security": "secured"},
                    {"name": "Cash Credit Facility - Bank B", "amount": 300.00, "lender": "Bank B", "security": "secured"},
                    {"name": "Bank Overdraft", "amount": 136.10, "lender": "Multiple Banks", "security": "secured"}
                ]
            },
            "unsecured_debt": {
                "total": 243.10,
                "details": [
                    {"name": "Current maturities of long-term debt", "amount": 200.00, "lender": "Banks/FIs", "security": "unsecured"},
                    {"name": "Unsecured loan from related party", "amount": 43.10, "lender": "Related Party", "security": "unsecured"}
                ]
            }
        },
        "other_liabilities": {
            "trade_payables": 4150.30,
            "provisions": 917.70,
            "deferred_tax_liabilities": 1500.00,
            "other_current_liabilities": 850.00
        }
    }
    
    # Test the build_final_output_clean method
    try:
        result = processor.build_final_output_clean(mock_extraction_data, "INR", 2024)
        
        print("✅ Enhanced extraction completed successfully!")
        print("\n📊 Results:")
        print("-" * 40)
        
        # Check debt_equity_analysis
        debt_analysis = result.get('debt_equity_analysis', [])
        if debt_analysis and debt_analysis[0]:
            analysis = debt_analysis[0]
            print(f"📈 Debt-Equity Analysis for {analysis.get('year', 'N/A')}:")
            print(f"  • Equity: {analysis.get('equity', {}).get('value', 'N/A'):,} ({analysis.get('equity', {}).get('percentage', 0)*100:.2f}%)")
            print(f"  • Long-term: {analysis.get('long_term', {}).get('value', 'N/A'):,} ({analysis.get('long_term', {}).get('percentage', 0)*100:.2f}%)")
            print(f"  • Short-term: {analysis.get('short_term', {}).get('value', 'N/A'):,} ({analysis.get('short_term', {}).get('percentage', 0)*100:.2f}%)")
            print(f"  • Currency: {analysis.get('currency_unit', 'N/A')}")
        
        # Check enhanced short-term debt details
        equity_liability = result.get('equity_liability', {})
        short_term_debt = equity_liability.get('short_term_debt', {})
        
        print(f"\n🔍 Enhanced Short-term Debt Details:")
        print(f"  • Interest Rates: {short_term_debt.get('interest', 'N/A')}")
        
        # Senior debt details
        senior_debt = short_term_debt.get('senior_debt', {})
        print(f"\n💰 Senior Debt (Secured): {senior_debt.get('value', 'N/A'):,}")
        senior_details = senior_debt.get('investor_details', [])
        for i, detail in enumerate(senior_details, 1):
            if detail.get('value'):
                print(f"    {i}. {detail.get('name', 'N/A')}: {detail.get('value', 'N/A'):,} ({detail.get('type', 'N/A')})")
        
        # Junior debt details
        junior_debt = short_term_debt.get('junior_debt', {})
        print(f"\n📋 Junior Debt (Unsecured): {junior_debt.get('value', 'N/A'):,}")
        junior_details = junior_debt.get('investor_details', [])
        for i, detail in enumerate(junior_details, 1):
            if detail.get('value'):
                print(f"    {i}. {detail.get('name', 'N/A')}: {detail.get('value', 'N/A'):,} ({detail.get('type', 'N/A')})")
        
        print(f"\n✅ Test completed successfully!")
        print(f"📝 Enhanced short-term debt extraction is working correctly.")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enhanced_extraction()
    sys.exit(0 if success else 1)
