import json
import sys
import os

# Currency unit multipliers
unit_multipliers = {
    "thousand": 1e3, "thousands": 1e3,
    "lakh": 1e5, "lakhs": 1e5,
    "crore": 1e7, "crores": 1e7,
    "million": 1e6, "millions": 1e6,
    "billion": 1e9, "billions": 1e9,
    "trillion": 1e12, "trillions": 1e12,
    "arab": 1e9, "arabs": 1e9
}

def get_multiplier(currency_unit):
    if not currency_unit or not isinstance(currency_unit, str):
        return None
    return unit_multipliers.get(currency_unit.strip().lower())

def convert_value(val, multiplier):
    return int(val * multiplier) if isinstance(val, (int, float)) else val

def convert_assets(assets):
    multiplier = get_multiplier(assets.get("currency_unit"))
    if not multiplier: return assets
    for section in ["current_assets", "other_non_current_assets", "property_plant_equipment"]:
        for item in assets.get(section, {}).get("asset_type", []):
            if "value" in item:
                item["value"] = convert_value(item["value"], multiplier)
    return assets

def convert_cashflows(cashflows):
    for entry in cashflows:
        multiplier = get_multiplier(entry.get("currency_unit"))
        if not multiplier: continue
        for field in ["capex", "cash_from_operations", "free_cash_flow"]:
            if field in entry:
                entry[field] = convert_value(entry[field], multiplier)
    return cashflows

def convert_revenue_expenses(data):
    for entry in data:
        multiplier = get_multiplier(entry.get("currency_unit"))
        if not multiplier: continue
        for field in ["corporate_tax", "depreciation", "exceptions_before_tax", "fuel_expenses", "interest", "employee_benifits_expenses", "other_expenses"]:
            if field in entry.get("expenses", {}):
                entry["expenses"][field] = convert_value(entry["expenses"][field], multiplier)
        for field in ["other_income", "revenue_operations"]:
            if field in entry.get("revenue", {}):
                entry["revenue"][field] = convert_value(entry["revenue"][field], multiplier)
    return data

def convert_debt_equity_analysis(data):
    for entry in data:
        multiplier = get_multiplier(entry.get("currency_unit"))
        if not multiplier: continue
        for key in ["equity", "long_term", "short_term"]:
            if "value" in entry.get(key, {}):
                entry[key]["value"] = convert_value(entry[key]["value"], multiplier)
    return data

def convert_equity_liability_recursive(data):
    multiplier = get_multiplier(data.get("currency_unit"))
    if not multiplier:
        return data

    def recurse(obj):
        if isinstance(obj, dict):
            for k, v in obj.items():
                if k in ["value", "no_of_shares"] and isinstance(v, (int, float)):
                    obj[k] = convert_value(v, multiplier)
                else:
                    recurse(v)
        elif isinstance(obj, list):
            for item in obj:
                recurse(item)
        return obj

    return recurse(data)

def convert_currency_units(data):
    if "assets" in data: data["assets"] = convert_assets(data["assets"])
    if "cashflows" in data: data["cashflows"] = convert_cashflows(data["cashflows"])
    if "revenue_expenses" in data: data["revenue_expenses"] = convert_revenue_expenses(data["revenue_expenses"])
    if "debt_equity_analysis" in data: data["debt_equity_analysis"] = convert_debt_equity_analysis(data["debt_equity_analysis"])
    if "equity_liability" in data: data["equity_liability"] = convert_equity_liability_recursive(data["equity_liability"])
    return data

def remove_currency_units(data):
    if "assets" in data: data["assets"].pop("currency_unit", None)
    for section in ["cashflows", "revenue_expenses", "debt_equity_analysis"]:
        if section in data:
            for entry in data[section]:
                if isinstance(entry, dict):
                    entry.pop("currency_unit", None)
    if "equity_liability" in data:
        data["equity_liability"].pop("currency_unit", None)
    return data



