#!/usr/bin/env python3
"""
Test script to verify enhanced balance sheet detection
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_balance_sheet_detection():
    """Test the enhanced balance sheet detection logic"""
    
    print("🧪 Testing Enhanced Balance Sheet Detection")
    print("=" * 60)
    
    try:
        from classify import EnhancedPDFFinancialClassifier
        classifier = EnhancedPDFFinancialClassifier()
        
        # Test the prompt content for balance sheet detection
        prompt = classifier.ENHANCED_CLASSIFICATION_PROMPT
        
        print("🔍 Checking Balance Sheet Detection Features:")
        print("-" * 50)
        
        # Check for enhanced balance sheet detection
        balance_sheet_checks = [
            ("Balance Sheet\" OR \"Statement of Financial Position\" OR \"Statement of Financial Condition\" OR \"Financial Position\" OR \"Statement of Assets and Liabilities\"", "Extended title variations"),
            ("As at [specific date]\" OR \"As on [date]\"", "Flexible date formats"),
            ("basic balance sheet structure with ASSETS and LIABILITIES", "Structure-based detection"),
            ("Current assets, Non-current assets, Current liabilities, Non-current liabilities", "Typical line items"),
            ("Be FLEXIBLE with formatting variations", "Flexibility instruction"),
            ("SPECIAL ATTENTION FOR BALANCE SHEETS", "Special attention section"),
            ("Balance sheets are often missed", "Missing detection warning"),
            ("focus on content structure", "Content over title emphasis")
        ]
        
        balance_sheet_score = 0
        for check_text, description in balance_sheet_checks:
            if check_text in prompt:
                print(f"  ✅ {description}: Found")
                balance_sheet_score += 1
            else:
                print(f"  ❌ {description}: Missing")
        
        print(f"\n📊 Balance Sheet Detection Score: {balance_sheet_score}/{len(balance_sheet_checks)}")
        
        # Check for potential issues
        print(f"\n🔍 Checking for Potential Issues:")
        print("-" * 50)
        
        potential_issues = [
            ("MUST have title containing", "Too strict title requirement"),
            ("MUST have date", "Too strict date requirement"),
            ("MUST show the structure", "Too strict structure requirement"),
            ("MUST include table sections", "Too strict section requirement")
        ]
        
        strict_requirements = 0
        for issue_text, description in potential_issues:
            if issue_text in prompt:
                print(f"  ⚠️  {description}: Found (might be too strict)")
                strict_requirements += 1
            else:
                print(f"  ✅ {description}: Not found (good)")
        
        # Analyze balance sheet specific improvements
        print(f"\n📋 Balance Sheet Improvements Analysis:")
        print("-" * 50)
        
        improvements = [
            ("WITHOUT \"Consolidated\" prefix at the beginning", "Allows mid-title consolidated"),
            ("SHOULD include typical balance sheet sections", "Changed from MUST to SHOULD"),
            ("Look for balance sheet line items", "Added line item guidance"),
            ("Be FLEXIBLE with formatting variations", "Added flexibility"),
            ("Even if formatting is unusual", "Unusual format handling"),
            ("Don't be too strict on exact title matching", "Relaxed title matching")
        ]
        
        improvement_score = 0
        for improvement_text, description in improvements:
            if improvement_text in prompt:
                print(f"  ✅ {description}: Implemented")
                improvement_score += 1
            else:
                print(f"  ❌ {description}: Not implemented")
        
        print(f"\n🎯 Enhancement Summary:")
        print("=" * 60)
        
        total_score = balance_sheet_score + improvement_score
        max_score = len(balance_sheet_checks) + len(improvements)
        
        print(f"📊 Overall Enhancement Score: {total_score}/{max_score}")
        print(f"🔍 Balance Sheet Detection Features: {balance_sheet_score}/{len(balance_sheet_checks)}")
        print(f"🚀 Flexibility Improvements: {improvement_score}/{len(improvements)}")
        print(f"⚠️  Strict Requirements Found: {strict_requirements}")
        
        if total_score >= max_score * 0.8 and strict_requirements <= 2:
            print(f"\n✅ Enhanced balance sheet detection looks good!")
            print(f"📋 The classifier should now:")
            print(f"   • Detect more balance sheet title variations")
            print(f"   • Be more flexible with date formats")
            print(f"   • Focus on content structure over strict titles")
            print(f"   • Handle unusual formatting better")
            print(f"   • Give special attention to balance sheets")
            return True
        else:
            print(f"\n❌ Balance sheet detection needs further improvement!")
            print(f"💡 Consider:")
            print(f"   • Adding more title variations")
            print(f"   • Reducing strict requirements")
            print(f"   • Adding more flexibility instructions")
            return False
            
    except Exception as e:
        print(f"❌ Error testing balance sheet detection: {e}")
        import traceback
        traceback.print_exc()
        return False

def suggest_troubleshooting():
    """Suggest troubleshooting steps for page 60"""
    print(f"\n🔧 Troubleshooting Page 60 Balance Sheet Issue:")
    print("=" * 60)
    
    print(f"📋 Possible Reasons Page 60 Was Missed:")
    print(f"1. 🏢 CONSOLIDATED ONLY: Page 60 might have only 'Consolidated Balance Sheet' without standalone")
    print(f"2. 📝 UNUSUAL TITLE: Balance sheet might have non-standard title")
    print(f"3. 📅 DATE FORMAT: Date might be in unexpected format")
    print(f"4. 🎨 FORMATTING: Unusual table structure or layout")
    print(f"5. 🔤 LANGUAGE: Non-English terms or mixed language")
    
    print(f"\n💡 Recommended Actions:")
    print(f"1. 🔍 MANUAL CHECK: Examine page 60 image to see actual content")
    print(f"2. 📋 TITLE CHECK: Look for exact title on page 60")
    print(f"3. 🏢 CONSOLIDATED CHECK: See if it says 'Consolidated Balance Sheet'")
    print(f"4. 🧪 TEST RUN: Run classification again with enhanced detection")
    print(f"5. 📝 LOG ANALYSIS: Check detailed logs for classification reasoning")
    
    print(f"\n🚀 Quick Fix Options:")
    print(f"1. If page 60 has unusual title → Add title variation to prompt")
    print(f"2. If page 60 is consolidated-only → This is correct behavior (should be rejected)")
    print(f"3. If page 60 has both standalone and consolidated → Should be detected now")
    print(f"4. If page 60 has unusual format → Enhanced flexibility should help")

if __name__ == "__main__":
    print("🚀 Starting Balance Sheet Detection Test")
    print("=" * 60)
    
    # Test enhanced detection
    detection_test_passed = test_balance_sheet_detection()
    
    # Provide troubleshooting guidance
    suggest_troubleshooting()
    
    print(f"\n🎯 Test Results:")
    print("=" * 60)
    
    if detection_test_passed:
        print("✅ Enhanced balance sheet detection implemented!")
        print("🔄 Try running the classification again - page 60 should now be detected")
    else:
        print("❌ Balance sheet detection needs more work")
        print("🔧 Additional enhancements may be needed")
    
    print(f"\n📝 Next Steps:")
    print(f"1. Run classification again on the same PDF")
    print(f"2. Check if page 60 is now classified as 'Statement of Balance Sheet'")
    print(f"3. If still not working, examine page 60 content manually")
    print(f"4. Adjust prompt based on actual page 60 content")
