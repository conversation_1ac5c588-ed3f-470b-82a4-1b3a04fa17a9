"""
Enhanced PDF Financial Document Classifier with Standard Name Enforcement
Author: Gangatharangurusamy
Date: 2025-06-09
Enhanced: Added notes detection and numbering system
Latest: 2025-06-25 - Fixed page alignment issue in batch processing
"""

import os
import json
import csv
import re
from typing import List, Dict, Tuple, Optional
import base64
from pathlib import Path
import argparse
import logging
from datetime import datetime
import shutil
import time
from collections import defaultdict

# Load environment variables from .env file
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("Warning: python-dotenv not installed. Install with: pip install python-dotenv")

# Required imports
try:
    from pdf2image import convert_from_path
    from langchain_google_genai import ChatGoogleGenerativeAI
    from langchain_core.messages import HumanMessage
    from PIL import Image
except ImportError as e:
    print(f"Missing required library: {e}")
    print("Install with: pip install pdf2image langchain-google-genai pillow python-dotenv")
    exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_pdf_classifier.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedPDFFinancialClassifier:
    """
    Enhanced classifier with standard name enforcement at AI level
    """
    
    # STANDARDIZED FINANCIAL CATEGORIES (ALWAYS USED)
    FINANCIAL_CATEGORIES = [
        "Statement of Balance Sheet",
        "Statement of Profit and Loss", 
        "Statement of Cash Flows",
        "Statement of Changes in Equity",
        "Credit Rating",
        "Notes to Financial Statements",
        "Irrelevant / Miscellaneous Page"
    ]

    # APPROACH 1: SIMPLIFIED PROMPT THAT FORCES STANDARD NAMES
    ENHANCED_CLASSIFICATION_PROMPT = """
    You are analyzing scanned pages from a company's annual financial report.

    🚨 CRITICAL: ALWAYS USE THESE EXACT STANDARD CLASSIFICATION NAMES:
    - "Statement of Balance Sheet" (even if document says "Statement of Financial Position", "Balance Sheet", etc.)
    - "Statement of Profit and Loss" (even if document says "Statement of Comprehensive Income", "Income Statement", etc.)
    - "Statement of Cash Flows" (even if document says "Cash Flow Statement", etc.)
    - "Statement of Changes in Equity" (even if document says "Statement of Shareholders' Equity", etc.)
    - "Credit Rating"
    - "Notes to Financial Statements"
    - "Irrelevant / Miscellaneous Page"

    🚨 SPECIAL INSTRUCTION FOR COMBINED STATEMENTS:
    - If a SINGLE page contains MULTIPLE financial statements (e.g., Balance Sheet + Profit & Loss on same page), classify it into ALL relevant categories
    - Return MULTIPLE classification objects for the SAME page number
    - This ensures the page gets saved in all appropriate folders

    🚨 CRITICAL STANDALONE vs CONSOLIDATED DETECTION LOGIC:

    STEP 1: First check if the page contains ONLY consolidated statements:
    - If page has ONLY "Consolidated Balance Sheet" (no standalone "Balance Sheet") → Classify as "Irrelevant / Miscellaneous Page"
    - If page has ONLY "Consolidated Statement of Profit and Loss" (no standalone P&L) → Classify as "Irrelevant / Miscellaneous Page"
    - If page has ONLY "Consolidated Statement of Cash Flows" (no standalone Cash Flow) → Classify as "Irrelevant / Miscellaneous Page"
    - If page has ONLY "Consolidated Statement of Changes in Equity" (no standalone Changes in Equity) → Classify as "Irrelevant / Miscellaneous Page"

    STEP 2: If page has BOTH standalone AND consolidated on same page:
    - Classify based on the standalone statement type (ignore the consolidated version)
    - Example: Page has both "Balance Sheet" and "Consolidated Balance Sheet" → Classify as "Statement of Balance Sheet"

    STEP 3: If page has ONLY standalone statements (no consolidated):
    - Classify normally based on the rules below

    STRICT CLASSIFICATION RULES – Apply the following EXACT criteria to classify each page:

    1. Statement of Balance Sheet:
    - MUST have title containing ANY of: "Balance Sheet" OR "Statement of Financial Position" OR "Statement of Financial Condition" OR "Financial Position" OR "Statement of Assets and Liabilities" (WITHOUT "Consolidated" prefix at the beginning)
    - MUST have date: "As at [specific date]" OR "As on [date]" OR similar date reference (e.g., "As at 31st March 2024")
    - MUST show the basic balance sheet structure with ASSETS and LIABILITIES sections
    - SHOULD include typical balance sheet sections like: Assets, Liabilities, Equity, Share Capital, Reserves, Borrowings
    - Look for balance sheet line items: Current assets, Non-current assets, Current liabilities, Non-current liabilities, Equity share capital, Retained earnings
    - ALWAYS classify as "Statement of Balance Sheet" regardless of document's actual title
    - REJECT if ONLY consolidated version exists without standalone
    - Be FLEXIBLE with formatting variations and international standards

    2. Statement of Profit and Loss:
    - MUST have title containing: "Statement of Profit and Loss" OR "Statement of Comprehensive Income" OR "Income Statement" OR "Profit & Loss Statement" OR "Statement of Operations" (WITHOUT "Consolidated" prefix)
    - If title is not present, assume it is NOT a valid statement
    - MUST have period: "For the year ended [date]"
    - MUST include a table with: Revenue, Other Income, Expenses, Subtotals, and Net Profit/Loss
    - ALWAYS classify as "Statement of Profit and Loss" regardless of document's actual title
    - REJECT if ONLY consolidated version exists without standalone

    3. Statement of Cash Flows:
    - MUST have title containing ANY of: "Cash Flow", "Cash Flows", "Statement of Cash Flow", "Statement of Cash Flows" (WITHOUT "Consolidated" prefix)
    - MUST have period: "For the year ended [date]" OR "Year ended [date]"
    - MUST include cash flow activities sections with ANY of these wordings:
      * Operating Activities / Cash flows from operating activities / Operating cash flows
      * Investing Activities / Cash flows from investing activities / Investment cash flows
      * Financing Activities / Cash flows from financing activities / Financing cash flows
    - MUST include cash flow amounts/numbers in a structured table format
    - MUST show net cash flow movements or opening/closing cash balances
    - ALWAYS classify as "Statement of Cash Flows" regardless of document's actual title
    - REJECT if ONLY consolidated version exists without standalone

     4. Statement of Changes in Equity:
    - MUST have title: "Statement of Changes in Equity" OR "Statement of Shareholders' Equity" (WITHOUT "Consolidated" prefix)
    - MUST include columns for equity components: Share Capital, Retained Earnings, Reserves (or similar)
    - MUST show: Opening balance, changes during the period, and closing balance
    - ALWAYS classify as "Statement of Changes in Equity" regardless of document's actual title
    - REJECT if ONLY consolidated version exists without standalone

    5. Credit Rating:
    - MUST contain any of the following credit rating agencies: CRISIL, ICRA, CARE, Moody's, S&P, Fitch
    - MUST contain valid rating symbols: AAA, AA+, AA, A+, A, BBB, etc.
    - MUST include one or more of these terms: "Credit Rating", "Rating Rationale", "Rating Outlook"
    - MUST have a table with columns: Instrument, Amount Rated, Rating Assigned, Outlook, Rating Agency
    - ALWAYS classify as "Credit Rating"

    6. Notes to Financial Statements:
    - A page MUST include a visible, clear title: "Notes forming part of Financial Statements" OR "Notes to Financial Statements" OR "Notes to the Financial Statements" (case-insensitive)
    - A page is classified as "Notes to Financial Statements" ONLY if it ALSO contains at least one valid note label in this format:
        - "Note 1", "Note-1", "Notes 1", or "Notes-1" (case-insensitive, with or without space/dash)
    - DO NOT classify a page as a note if it only contains section numbers such as:
        - "1. Revenue", "2. Tax", etc. — These are not valid note labels
    - if alone number like "1.", "2.", etc. is present without "Note" or "Notes", it is NOT a valid note Please Don't classify it as a note
    - If one or more valid note labels are found, extract them and include them in the `note_numbers` array
    - If NO valid note labels are found, return an empty `note_numbers` array and DO NOT classify the page as a note
    - ALWAYS classify as "Notes to Financial Statements"

    STRICT RULE: If the term "Note" (or variation listed above) is NOT explicitly written on the page, DO NOT classify it as a note — even if there are numbers like "1.", "2.", etc.

    7. Irrelevant / Miscellaneous Page:
    - Everything else (e.g., Director's Report, Auditor's Report, Cover pages)
    - Pages without clear financial statement structure
    - ALWAYS classify as "Irrelevant / Miscellaneous Page"

    IMPORTANT:
    - Analyze all images together — continuation pages may not repeat titles
    - Use your best judgment to classify continuation pages if the structure clearly matches an earlier page type
    - If a single page contains multiple statements, return multiple classification entries for the same page number
    - ALWAYS USE THE EXACT STANDARD NAMES LISTED ABOVE - DO NOT use document's original titles
    - Use the exact page numbers shown in the image labels (they represent actual PDF page numbers)

    🚨 CONSOLIDATED vs STANDALONE FINAL CHECK:
    - ALWAYS apply the 3-step consolidated detection logic from above
    - If ONLY consolidated statements exist → "Irrelevant / Miscellaneous Page"
    - If BOTH standalone and consolidated exist → Classify based on standalone
    - If ONLY standalone exists → Classify normally
    - This rule overrides all other classification criteria

    🔍 SPECIAL ATTENTION FOR BALANCE SHEETS:
    - Balance sheets are often missed - look carefully for ANY page with Assets and Liabilities structure
    - Common balance sheet titles: "Balance Sheet", "Statement of Financial Position", "Assets and Liabilities"
    - Look for typical balance sheet items: Share Capital, Reserves, Current Assets, Fixed Assets, Current Liabilities, Long-term Debt
    - Even if formatting is unusual, if it shows Assets = Liabilities + Equity structure, it's likely a balance sheet
    - Don't be too strict on exact title matching - focus on content structure
    
    🔍 EXAMPLES FOR CONSOLIDATED vs STANDALONE DETECTION:

    Example 1 - ONLY Consolidated (REJECT):
    Page has title "Consolidated Balance Sheet" with no standalone "Balance Sheet"
    → {"page_number": 42, "classification": "Irrelevant / Miscellaneous Page", "note_numbers": []}

    Example 2 - BOTH Standalone and Consolidated (ACCEPT):
    Page has both "Balance Sheet" and "Consolidated Balance Sheet"
    → {"page_number": 42, "classification": "Statement of Balance Sheet", "note_numbers": []}

    Example 3 - ONLY Standalone (ACCEPT):
    Page has only "Balance Sheet" with no consolidated version
    → {"page_number": 42, "classification": "Statement of Balance Sheet", "note_numbers": []}

    Example 4 - Multiple Statements on Same Page:
    If page 42 contains BOTH a standalone Balance Sheet AND a standalone Profit & Loss:
    [
    {"page_number": 42, "classification": "Statement of Balance Sheet", "note_numbers": []},
    {"page_number": 42, "classification": "Statement of Profit and Loss", "note_numbers": []}
    ]

    RETURN ONLY this format as a JSON array (ALWAYS use the exact page numbers from image labels):
    [
    {"page_number": 41, "classification": "Statement of Profit and Loss", "note_numbers": []},
    {"page_number": 42, "classification": "Statement of Balance Sheet", "note_numbers": []},
    {"page_number": 43, "classification": "Statement of Cash Flows", "note_numbers": []},
    {"page_number": 44, "classification": "Notes to Financial Statements", "note_numbers": ["1", "2"]},
    {"page_number": 45, "classification": "Irrelevant / Miscellaneous Page", "note_numbers": []}
    ]

    CRITICAL:
    - Use the EXACT page numbers shown in the image labels
    - Always include the `note_numbers` field — use an empty list if no notes are present
    - For combined pages, return multiple entries with the same page number
    - ALWAYS use the EXACT standard classification names listed at the top
    - Do NOT return any explanation, summary, or text outside the JSON array
    """

    def __init__(self, batch_size: int = 5):
        """Initialize the enhanced classifier with standard name enforcement"""
        self.batch_size = batch_size
        
        # Check for API key
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("GEMINI_API_KEY not found. Please set it in .env file")
        
        # Initialize Gemini model
        self.llm = ChatGoogleGenerativeAI(
            model="gemini-2.0-flash-001",
            temperature=0,
            max_tokens=None,
            timeout=None,
            google_api_key=api_key,
            max_retries=2,
        )
        
        # Initialize tracking variables
        self.all_results = []
        self.saved_images_by_classification = {}
        self.notes_index = defaultdict(list)  # {note_number: [page_info]}
        
        logger.info(f"Initialized Enhanced PDF Classifier with standard name enforcement, batch size: {batch_size}")
        logger.info(f"Standard categories enforced: {len(self.FINANCIAL_CATEGORIES)} types")

    def pdf_to_images(self, pdf_path: str, output_dir: str = None) -> List[str]:
        """Convert PDF to images"""
        pdf_path = Path(pdf_path)
        if not pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
            
        if output_dir is None:
            output_dir = pdf_path.parent / f"{pdf_path.stem}_images"
        
        output_dir = Path(output_dir)
        
        output_dir.mkdir(exist_ok=True)
        logger.info(f"Converting PDF to images: {pdf_path}")
        
        try:
            images = convert_from_path(pdf_path, dpi=200, fmt='PNG')
            
            image_paths = []
            for i, image in enumerate(images, 1):
                image_path = output_dir / f"page_{i:04d}.png"
                image.save(image_path, 'PNG')
                image_paths.append(str(image_path))
                
            logger.info(f"Converted {len(images)} pages to images in {output_dir}")
            return image_paths
            
        except Exception as e:
            logger.error(f"Error converting PDF to images: {e}")
            raise

    def encode_image_base64(self, image_path: str) -> str:
        """Encode image to base64 for API"""
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')

    def validate_classification(self, classification: str) -> str:
        """Validate and enforce standard classification names"""
        if classification in self.FINANCIAL_CATEGORIES:
            return classification
        else:
            # If AI returns non-standard name, log warning and use fallback
            logger.warning(f"⚠️ Non-standard classification detected: '{classification}' - using fallback")
            return "Irrelevant / Miscellaneous Page"

    def classify_batch(self, image_paths: List[str], start_page: int) -> List[Dict]:
        """Classify a batch of images with standard name enforcement"""
        try:
            logger.info(f"🔍 PROCESSING BATCH: Pages {start_page} to {start_page + len(image_paths) - 1}")
            
            content = [{"type": "text", "text": self.ENHANCED_CLASSIFICATION_PROMPT}]
            
            # Add images to the content with ABSOLUTE page numbers
            for i, image_path in enumerate(image_paths):
                image_base64 = self.encode_image_base64(image_path)
                actual_page_number = start_page + i  # Calculate absolute page number
                
                content.append({
                    "type": "text", 
                    "text": f"\nPage {actual_page_number}:"  # ✅ FIXED: Use absolute page numbers
                })
                content.append({
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/png;base64,{image_base64}"
                    }
                })
            
            # Create message and invoke model
            message = HumanMessage(content=content)
            response = self.llm.invoke([message])
            
            # Parse JSON response
            response_text = response.content.strip()
            
            # Clean response text
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            response_text = response_text.strip()
            
            try:
                classifications = json.loads(response_text)
                
                # Validate and correct classifications
                corrected_classifications = []
                for classification_result in classifications:
                    page_number = classification_result.get('page_number')
                    raw_classification = classification_result.get('classification', 'Irrelevant / Miscellaneous Page')
                    
                    # Validate page number is within expected range
                    if page_number and start_page <= page_number < start_page + len(image_paths):
                        # VALIDATE AND ENFORCE STANDARD NAMES
                        validated_classification = self.validate_classification(raw_classification)
                        
                        corrected_item = {
                            'page_number': page_number,  # ✅ FIXED: Use AI's returned page number (should be absolute now)
                            'classification': validated_classification,  # Always standard name
                            'note_numbers': classification_result.get('note_numbers', [])
                        }
                        corrected_classifications.append(corrected_item)
                        
                        # Index notes for later retrieval
                        if corrected_item['note_numbers']:
                            for note_num in corrected_item['note_numbers']:
                                image_index = page_number - start_page
                                if 0 <= image_index < len(image_paths):
                                    self.notes_index[note_num].append({
                                        'page_number': page_number,
                                        'image_path': image_paths[image_index],
                                        'classification': validated_classification
                                    })
                        
                        # Standard logging
                        logger.info(f"🔍 Page {page_number}: {validated_classification} | Notes: {corrected_item['note_numbers']}")
                    else:
                        logger.warning(f"⚠️ Page number {page_number} out of expected range {start_page}-{start_page + len(image_paths) - 1}")
                
                return corrected_classifications
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response: {e}")
                logger.error(f"Response text: {response_text}")
                return []
                
        except Exception as e:
            logger.error(f"Error classifying batch starting at page {start_page}: {e}")
            return []

    def generate_smart_filename(self, classification: str, page_num: int, note_numbers: List[str]) -> str:
        """Generate smart filename based on standard classification and notes"""
        safe_name = classification.replace('/', '_').replace(' ', '_').lower()
        
        if classification == "Notes to Financial Statements" and note_numbers:
            # For notes, create specific naming
            note_str = "_".join(note_numbers)
            
            # Check if this note combination already exists
            existing_count = 0
            for existing_result in self.all_results:
                if (existing_result.get('classification') == classification and 
                    existing_result.get('note_numbers') == note_numbers and
                    existing_result.get('page_number') < page_num):
                    existing_count += 1
            
            if existing_count > 0:
                return f"page_{page_num:04d}_notes_{note_str}_{existing_count + 1}.png"
            else:
                return f"page_{page_num:04d}_notes_{note_str}.png"
        else:
            return f"page_{page_num:04d}_{safe_name}.png"

    def save_batch_images_immediately(self, batch_results: List[Dict], all_image_paths: List[str], output_dir: Path, batch_num: int) -> int:
        """Save classified images with standard naming"""
        logger.info(f"💾 Saving images for batch {batch_num} immediately...")
        
        classified_dir = output_dir / "classified_images"
        classified_dir.mkdir(exist_ok=True)
        
        saved_count = 0
        
        for result in batch_results:
            classification = result['classification']  # Always standard name now
            page_num = result['page_number']
            note_numbers = result.get('note_numbers', [])
            
            # Skip irrelevant pages
            if classification == 'Irrelevant / Miscellaneous Page':
                logger.info(f"⏭️  Skipping irrelevant page {page_num}")
                continue
            
            # Create classification directory using STANDARD name
            safe_name = classification.replace('/', '_').replace(' ', '_').lower()
            class_dir = classified_dir / safe_name
            class_dir.mkdir(exist_ok=True)
            
            # Initialize tracking if new classification
            if classification not in self.saved_images_by_classification:
                self.saved_images_by_classification[classification] = []
            
            # Copy image with smart filename
            if page_num <= len(all_image_paths):
                source_image = all_image_paths[page_num - 1]
                target_filename = self.generate_smart_filename(classification, page_num, note_numbers)
                target_path = class_dir / target_filename
                
                # Copy the image
                shutil.copy2(source_image, target_path)
                self.saved_images_by_classification[classification].append(str(target_path))
                saved_count += 1
                
                # Standard logging
                if note_numbers:
                    logger.info(f"💾 SAVED: Page {page_num} → {classification} (Notes: {', '.join(note_numbers)})")
                    print(f"  ✅ Saved: Page {page_num} → {classification} (Notes: {', '.join(note_numbers)})")
                else:
                    logger.info(f"💾 SAVED: Page {page_num} → {classification}")
                    print(f"  ✅ Saved: Page {page_num} → {classification}")
            else:
                logger.error(f"❌ Page {page_num} index out of range (total: {len(all_image_paths)})")
        
        logger.info(f"📦 Batch {batch_num}: Saved {saved_count} classified images")
        return saved_count

    def classify_pdf_batch_by_batch(self, pdf_path: str, output_dir: str = None, max_pages: int = None) -> List[Dict]:
        """Process PDF in batches with standard name enforcement"""
        pdf_path = Path(pdf_path)
        
        if output_dir is None:
            output_dir = pdf_path.parent / f"{pdf_path.stem}_output"
        
        output_dir = Path(output_dir)
        output_dir.mkdir(exist_ok=True)
        
        # Convert PDF to images
        temp_images_dir = output_dir / "temp_images"
        image_paths = self.pdf_to_images(pdf_path, temp_images_dir)
        
        # Limit pages if specified
        if max_pages:
            image_paths = image_paths[:max_pages]
            logger.info(f"Limited processing to first {max_pages} pages")
        
        total_saved = 0
        
        print(f"\n🚀 Starting ENHANCED batch-by-batch processing with STANDARD NAME ENFORCEMENT...")
        print(f"📄 Total pages: {len(image_paths)}")
        print(f"📦 Batch size: {self.batch_size}")
        print(f"🎯 Processing with guaranteed standard naming for universal compatibility\n")
        
        try:
            # Process in batches
            for i in range(0, len(image_paths), self.batch_size):
                batch_images = image_paths[i:i + self.batch_size]
                start_page = i + 1
                batch_num = (i // self.batch_size) + 1
                
                print(f"📦 BATCH {batch_num}: Processing pages {start_page} to {start_page + len(batch_images) - 1}")
                
                # Classify batch
                batch_results = self.classify_batch(batch_images, start_page)
                
                if batch_results:
                    # Save images immediately
                    batch_saved = self.save_batch_images_immediately(batch_results, image_paths, output_dir, batch_num)
                    total_saved += batch_saved
                    
                    # Add to overall results
                    self.all_results.extend(batch_results)
                    
                    print(f"📦 BATCH {batch_num}: Saved {batch_saved} images")
                    
                    # Show classifications (always standard names)
                    for result in batch_results:
                        page_num = result['page_number']
                        classification = result['classification']
                        note_numbers = result.get('note_numbers', [])
                        
                        if classification != 'Irrelevant / Miscellaneous Page':
                            if note_numbers:
                                print(f"    🎯 Page {page_num}: {classification} (Notes: {', '.join(note_numbers)})")
                            else:
                                print(f"    🎯 Page {page_num}: {classification}")
                else:
                    print(f"❌ BATCH {batch_num}: Classification failed")
                
                time.sleep(2)
                print()
            
            # Create enhanced manifest with standard names
            self.create_enhanced_manifest(output_dir, pdf_path.name)
            
            # Save final results
            self.save_final_results(output_dir, pdf_path.name)
            
            # Clean up temp images
            logger.info("🧹 Cleaning up temporary images...")
            shutil.rmtree(temp_images_dir, ignore_errors=True)
            
            print(f"\n🎉 ENHANCED PROCESSING COMPLETE!")
            print(f"📄 Total pages processed: {len(image_paths)}")
            print(f"💾 Total images saved: {total_saved}")
            print(f"📝 Notes index created with {len(self.notes_index)} unique notes")
            print(f"🎯 ALL CLASSIFICATIONS USE STANDARD NAMES - Zero post-processing needed!")
            
            return self.all_results
            
        except Exception as e:
            logger.error(f"Error during enhanced processing: {e}")
            raise

    def create_enhanced_manifest(self, output_dir: Path, pdf_name: str):
        """Create enhanced manifest with standard naming guaranteed"""
        manifest = {
            "document_name": pdf_name,
            "processing_timestamp": datetime.now().isoformat(),
            "processing_method": "enhanced_batch_with_standard_name_enforcement",
            "total_classifications": len(self.saved_images_by_classification),
            "total_saved_images": sum(len(images) for images in self.saved_images_by_classification.values()),
            "notes_index": dict(self.notes_index),  # Convert defaultdict to dict
            "standard_categories_enforced": self.FINANCIAL_CATEGORIES,  # Document what standards are used
            "classifications": {}
        }
        
        # Add details for each classification (using standard names only)
        for classification, image_paths in self.saved_images_by_classification.items():
            safe_name = classification.replace('/', '_').replace(' ', '_').lower()
            
            manifest["classifications"][classification] = {
                "count": len(image_paths),
                "directory": f"classified_images/{safe_name}",
                "images": [Path(img).name for img in image_paths],
                "full_paths": image_paths
            }
        
        # Save enhanced manifest
        manifest_path = output_dir / "enhanced_vllm_manifest.json"
        with open(manifest_path, 'w', encoding='utf-8') as f:
            json.dump(manifest, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📋 Enhanced VLLM manifest with standard names saved: {manifest_path}")

    def save_final_results(self, output_dir: Path, pdf_name: str):
        """Save enhanced final results with standard names"""
        relevant_results = [
            result for result in self.all_results 
            if result.get('classification') != 'Irrelevant / Miscellaneous Page'
        ]
        
        for result in relevant_results:
            result['document'] = pdf_name
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save as JSON
        json_path = output_dir / f"{Path(pdf_name).stem}_enhanced_classifications_{timestamp}.json"
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(relevant_results, f, indent=2, ensure_ascii=False)
        
        # Save notes index separately
        notes_path = output_dir / f"{Path(pdf_name).stem}_notes_index_{timestamp}.json"
        with open(notes_path, 'w', encoding='utf-8') as f:
            json.dump(dict(self.notes_index), f, indent=2, ensure_ascii=False)
        
        # Save as CSV with standard names only
        csv_path = output_dir / f"{Path(pdf_name).stem}_enhanced_classifications_{timestamp}.csv"
        if relevant_results:
            with open(csv_path, 'w', newline='', encoding='utf-8') as f:
                fieldnames = ['document', 'page_number', 'classification', 'note_numbers']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                for result in relevant_results:
                    # Convert note_numbers list to string for CSV
                    csv_result = result.copy()
                    csv_result['note_numbers'] = ', '.join(result.get('note_numbers', []))
                    writer.writerow(csv_result)
        
        logger.info(f"Enhanced results saved to: {json_path}, {notes_path}, and {csv_path}")


def main():
    """Main function for enhanced processing with standard name enforcement"""
    parser = argparse.ArgumentParser(description="Enhanced: Classify financial documents with standard name enforcement")
    parser.add_argument("pdf_path", help="Path to the PDF file")
    parser.add_argument("--batch-size", type=int, default=5, help="Number of pages per batch")
    parser.add_argument("--output-dir", help="Output directory (default: same as PDF)")
    parser.add_argument("--max-pages", type=int, help="Maximum number of pages to process")
    
    args = parser.parse_args()
    
    try:
        # Initialize enhanced classifier
        classifier = EnhancedPDFFinancialClassifier(batch_size=args.batch_size)
        
        print(f"🎯 Standard Name Enforcement Enabled:")
        print(f"   • AI will ALWAYS use standard classification names")
        print(f"   • Zero post-processing mapping needed")
        print(f"   • Universal compatibility guaranteed")
        print(f"   • Standard categories: {len(classifier.FINANCIAL_CATEGORIES)} types\n")
        
        # Process PDF
        results = classifier.classify_pdf_batch_by_batch(
            pdf_path=args.pdf_path,
            output_dir=args.output_dir,
            max_pages=args.max_pages
        )
        
        # Print final summary
        relevant_results = [r for r in results if r.get('classification') != 'Irrelevant / Miscellaneous Page']
        
        print(f"\n✅ ENHANCED FINAL SUMMARY:")
        print(f"📄 Total relevant pages found: {len(relevant_results)}")
        
        if relevant_results:
            print("\n📊 Classification Summary (All Standard Names):")
            from collections import Counter
            classification_counts = Counter(result['classification'] for result in relevant_results)
            for classification, count in classification_counts.items():
                print(f"  • {classification}: {count} pages")
        
        # Show notes summary
        notes_summary = {}
        for result in relevant_results:
            if result.get('note_numbers'):
                for note_num in result['note_numbers']:
                    if note_num not in notes_summary:
                        notes_summary[note_num] = 0
                    notes_summary[note_num] += 1
        
        if notes_summary:
            print(f"\n📝 Notes Summary:")
            for note_num, count in sorted(notes_summary.items()):
                print(f"  • Note {note_num}: {count} pages")
        
        print(f"\n🎯 Perfect! All files saved with standard names - Bridge will find everything!")
        
    except Exception as e:
        logger.error(f"Enhanced processing failed: {e}")
        print(f"❌ Error: {e}")
        exit(1)


if __name__ == "__main__":
    main()